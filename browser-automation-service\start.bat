@echo off
echo 🚀 Starting RouKey Browser Automation Service...

REM Check if .env file exists
if not exist .env (
    echo ⚠️  .env file not found. Creating from template...
    copy .env.example .env
    echo ✅ Please edit .env file with your API keys and configuration
    echo 📝 Required: OPENAI_API_KEY, GOOGLE_SEARCH_API_KEY, GOOG<PERSON>_SEARCH_ENGINE_ID
    pause
    exit /b 1
)

REM Check if running in Docker
if "%1"=="docker" (
    echo 🐳 Starting with Docker Compose...
    docker-compose up -d
    echo ✅ Services started!
    echo 📊 Health check: http://localhost:8000/health/
    echo 📚 API docs: http://localhost:8000/docs
    pause
    exit /b 0
)

REM Check Python version
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.11+
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist venv (
    echo 📦 Creating virtual environment...
    python -m venv venv
)

echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

echo 📦 Installing dependencies...
pip install -r requirements.txt

echo 🎭 Installing Playwright browsers...
playwright install chromium --with-deps

echo ⚠️  Make sure <PERSON><PERSON> and Qdrant are running:
echo    Redis: docker run -d -p 6379:6379 redis:7-alpine
echo    Qdrant: docker run -d -p 6333:6333 qdrant/qdrant:latest
echo.

echo 🚀 Starting FastAPI server...
python main.py

pause
