# Browser Automation Service Dependencies
# Core FastAPI and async support
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.7.3
python-multipart>=0.0.6

# Browser Use and dependencies
browser-use>=0.3.1
playwright>=1.40.0

# LangGraph and <PERSON><PERSON><PERSON><PERSON>
langgraph>=0.2.16
langchain>=0.2.16
langchain-openai>=0.1.23
langchain-anthropic>=0.1.23
langchain-google-genai>=1.0.10
langchain-community>=0.2.16

# Memory functionality (Mem0) - RESTORED for commercial app
mem0ai>=0.1.11
qdrant-client>=1.7.0

# Google Search API
google-api-python-client>=2.108.0
google-auth>=2.23.4
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.2.0

# Database and caching
asyncpg>=0.29.0
redis>=5.0.1
aioredis>=2.0.1

# HTTP client and utilities
httpx>=0.25.2
aiofiles>=24.1.0
python-dotenv>=1.0.0

# Supabase integration
supabase>=2.3.0

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0

# Security and validation
cryptography>=41.0.0
python-jose[cryptography]>=3.3.0

# Development and testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
black>=23.11.0
isort>=5.12.0
mypy>=1.7.1
