#!/usr/bin/env python3
"""
Test Qdrant connection and setup for Browser Use memory
"""

import asyncio
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import numpy as np

async def test_qdrant_setup():
    """Test Qdrant setup for browser automation memory"""
    
    try:
        # Try to connect to local Qdrant instance
        print("Attempting to connect to local Qdrant instance...")
        client = QdrantClient(host="localhost", port=6333)
        
        # Test connection
        collections = client.get_collections()
        print(f"✅ Connected to Qdrant! Collections: {collections}")
        
    except Exception as e:
        print(f"❌ Local Qdrant not available: {e}")
        print("🔄 Trying in-memory Qdrant...")
        
        try:
            # Use in-memory Qdrant for development
            client = QdrantClient(":memory:")
            print("✅ In-memory Qdrant initialized!")
            
        except Exception as e2:
            print(f"❌ In-memory Qdrant failed: {e2}")
            return False
    
    # Create collection for browser automation memory
    collection_name = "browser_automation_memory"
    
    try:
        # Check if collection exists
        try:
            collection_info = client.get_collection(collection_name)
            print(f"✅ Collection '{collection_name}' already exists")
        except:
            # Create collection
            client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(size=384, distance=Distance.COSINE)  # For sentence transformers
            )
            print(f"✅ Created collection '{collection_name}'")
        
        # Test inserting a sample memory
        test_vector = np.random.random(384).tolist()
        test_point = PointStruct(
            id=1,
            vector=test_vector,
            payload={
                "task_id": "test_task",
                "step": "test_step",
                "content": "This is a test memory for browser automation",
                "timestamp": "2025-06-21T09:00:00Z"
            }
        )
        
        client.upsert(
            collection_name=collection_name,
            points=[test_point]
        )
        print("✅ Test memory inserted successfully")
        
        # Test searching
        search_results = client.search(
            collection_name=collection_name,
            query_vector=test_vector,
            limit=1
        )
        print(f"✅ Memory search successful: {len(search_results)} results")
        
        print("\n🎉 Qdrant setup complete and ready for Browser Use memory!")
        return True
        
    except Exception as e:
        print(f"❌ Collection setup failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_qdrant_setup())
    if success:
        print("\n✅ Qdrant is ready for browser automation!")
    else:
        print("\n❌ Qdrant setup failed!")
