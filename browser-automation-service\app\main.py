"""
Main FastAPI Application
Browser automation microservice with comprehensive API endpoints
"""

import asyncio
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

# Load environment variables at startup
load_dotenv()

from app.core.logging import LoggerMixin
from app.middleware.quota_middleware import quota_middleware
from app.api.browser_automation import router as browser_router
from app.api.chat_integration import router as chat_router
from app.api.quota_dashboard import router as quota_router
from app.api.test_endpoint import router as test_router
from app.api.user_config import router as user_config_router
from app.services.access_control import access_control_manager
from app.services.error_handler import error_handler
from app.services.fallback_manager import fallback_manager
# Removed langgraph_workflow_manager - using BrowserOrchestrator instead


class BrowserAutomationService(LoggerMixin):
    """Main browser automation service application"""
    
    def __init__(self):
        self.app = None
        self.startup_time = None
        self.log_info("Browser automation service initializing")
    
    @asynccontextmanager
    async def lifespan(self, app: FastAPI):
        """Application lifespan management"""
        try:
            # Startup
            self.startup_time = datetime.now()
            self.log_info("Starting browser automation service")
            
            # Initialize services
            await self._initialize_services()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.log_info("Browser automation service started successfully")
            
            yield
            
            # Shutdown
            self.log_info("Shutting down browser automation service")
            
            # Cleanup services
            await self._cleanup_services()
            
            self.log_info("Browser automation service shutdown complete")
            
        except Exception as e:
            self.log_error(f"Service lifecycle error: {e}")
            raise
    
    def create_app(self) -> FastAPI:
        """Create and configure FastAPI application"""
        try:
            # Create FastAPI app with lifespan
            self.app = FastAPI(
                title="RouKey Browser Automation Service",
                description="Comprehensive browser automation with LangGraph workflows",
                version="1.0.0",
                docs_url="/docs",
                redoc_url="/redoc",
                lifespan=self.lifespan
            )
            
            # Configure CORS
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],  # Configure appropriately for production
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # Add quota enforcement middleware
            self.app.middleware("http")(quota_middleware)
            
            # Add global exception handler
            self.app.exception_handler(Exception)(self._global_exception_handler)
            
            # Include routers
            self.app.include_router(browser_router)
            self.app.include_router(chat_router)
            self.app.include_router(quota_router)
            self.app.include_router(test_router)
            self.app.include_router(user_config_router)
            
            # Add health check endpoints
            self._add_health_endpoints()
            
            # Add service info endpoints
            self._add_info_endpoints()
            
            self.log_info("FastAPI application created and configured")
            
            return self.app
            
        except Exception as e:
            self.log_error(f"Failed to create FastAPI application: {e}")
            raise
    
    async def _initialize_services(self):
        """Initialize all services"""
        try:
            self.log_info("Initializing services")
            
            # Browser orchestrator is initialized on-demand
            
            # Initialize access control manager
            # (Already initialized in constructor)
            
            # Initialize error handler
            # (Already initialized in constructor)
            
            # Initialize fallback manager
            # (Already initialized in constructor)
            
            self.log_info("All services initialized successfully")
            
        except Exception as e:
            self.log_error(f"Service initialization failed: {e}")
            raise
    
    async def _start_background_tasks(self):
        """Start background maintenance tasks"""
        try:
            self.log_info("Starting background tasks")
            
            # Start quota cleanup task
            asyncio.create_task(self._quota_cleanup_task())
            
            # Start task cleanup task
            asyncio.create_task(self._task_cleanup_task())
            
            # Start cache cleanup task
            asyncio.create_task(self._cache_cleanup_task())
            
            self.log_info("Background tasks started")
            
        except Exception as e:
            self.log_error(f"Failed to start background tasks: {e}")
    
    async def _cleanup_services(self):
        """Cleanup all services"""
        try:
            self.log_info("Cleaning up services")
            
            # Browser orchestrator cleanup is handled automatically
            
            # Cleanup access control manager
            await access_control_manager.cleanup()
            
            # Cleanup error handler
            await error_handler.cleanup()
            
            # Cleanup fallback manager
            await fallback_manager.cleanup()
            
            self.log_info("All services cleaned up successfully")
            
        except Exception as e:
            self.log_error(f"Service cleanup failed: {e}")
    
    def _add_health_endpoints(self):
        """Add health check endpoints"""
        
        @self.app.get("/health")
        async def health_check():
            """Basic health check"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "browser-automation",
                "version": "1.0.0"
            }
        
        @self.app.get("/health/detailed")
        async def detailed_health_check():
            """Detailed health check with service status"""
            try:
                # Check service health
                service_health = {
                    "langgraph_workflow": await self._check_langgraph_health(),
                    "access_control": await self._check_access_control_health(),
                    "error_handler": await self._check_error_handler_health(),
                    "fallback_manager": await self._check_fallback_manager_health()
                }
                
                # Overall health status
                all_healthy = all(status["healthy"] for status in service_health.values())
                
                return {
                    "status": "healthy" if all_healthy else "degraded",
                    "timestamp": datetime.now().isoformat(),
                    "service": "browser-automation",
                    "version": "1.0.0",
                    "uptime_seconds": (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0,
                    "services": service_health
                }
                
            except Exception as e:
                self.log_error(f"Health check failed: {e}")
                return {
                    "status": "unhealthy",
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e)
                }
        
        @self.app.get("/health/ready")
        async def readiness_check():
            """Readiness check for Kubernetes"""
            try:
                # Check if all services are ready
                ready = (
                    access_control_manager is not None and
                    error_handler is not None and
                    fallback_manager is not None
                )
                
                if ready:
                    return {"status": "ready", "timestamp": datetime.now().isoformat()}
                else:
                    raise HTTPException(status_code=503, detail="Service not ready")
                    
            except Exception as e:
                self.log_error(f"Readiness check failed: {e}")
                raise HTTPException(status_code=503, detail=f"Service not ready: {e}")
        
        @self.app.get("/health/live")
        async def liveness_check():
            """Liveness check for Kubernetes"""
            return {
                "status": "alive",
                "timestamp": datetime.now().isoformat(),
                "pid": os.getpid() if 'os' in globals() else "unknown"
            }
    
    def _add_info_endpoints(self):
        """Add service information endpoints"""
        
        @self.app.get("/info")
        async def service_info():
            """Get service information"""
            return {
                "service": "RouKey Browser Automation Service",
                "version": "1.0.0",
                "description": "Comprehensive browser automation with LangGraph workflows",
                "startup_time": self.startup_time.isoformat() if self.startup_time else None,
                "endpoints": {
                    "browser_automation": "/api/browser",
                    "chat_integration": "/api/chat-integration",
                    "quota_dashboard": "/api/quota",
                    "testing": "/api/test",
                    "user_config": "/api/user-config",
                    "health": "/health",
                    "docs": "/docs"
                },
                "features": [
                    "LangGraph hierarchical workflows",
                    "Tier-based access control",
                    "Intelligent error handling",
                    "Fallback mechanisms",
                    "Chat integration",
                    "Real-time progress streaming",
                    "Quota management",
                    "Usage analytics"
                ]
            }
        
        @self.app.get("/metrics")
        async def service_metrics():
            """Get service metrics"""
            try:
                # Get metrics from various services
                access_analytics = await access_control_manager.get_access_analytics()
                error_analytics = await error_handler.get_error_analytics()
                fallback_analytics = await fallback_manager.get_fallback_analytics()
                
                return {
                    "timestamp": datetime.now().isoformat(),
                    "uptime_seconds": (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0,
                    "access_control": access_analytics,
                    "error_handling": error_analytics,
                    "fallback_management": fallback_analytics
                }
                
            except Exception as e:
                self.log_error(f"Metrics collection failed: {e}")
                return {
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e)
                }
    
    async def _global_exception_handler(self, request: Request, exc: Exception):
        """Global exception handler"""
        try:
            self.log_error(f"Unhandled exception: {exc}", request_path=request.url.path)
            
            # Handle specific exception types
            if isinstance(exc, HTTPException):
                return JSONResponse(
                    status_code=exc.status_code,
                    content={"error": exc.detail, "timestamp": datetime.now().isoformat()}
                )
            
            # Handle general exceptions
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": str(exc),
                    "timestamp": datetime.now().isoformat(),
                    "path": request.url.path
                }
            )
            
        except Exception as e:
            self.log_error(f"Exception handler failed: {e}")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Critical error",
                    "timestamp": datetime.now().isoformat()
                }
            )
    
    # Background task methods
    async def _quota_cleanup_task(self):
        """Background task for quota cleanup"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await quota_middleware.cleanup_rate_limit_cache()
                self.log_info("Quota cleanup completed")
            except Exception as e:
                self.log_error(f"Quota cleanup failed: {e}")
    
    async def _task_cleanup_task(self):
        """Background task for task cleanup"""
        while True:
            try:
                await asyncio.sleep(1800)  # Run every 30 minutes
                from app.api.browser_automation import browser_automation_api
                await browser_automation_api.cleanup_completed_tasks()
                self.log_info("Task cleanup completed")
            except Exception as e:
                self.log_error(f"Task cleanup failed: {e}")
    
    async def _cache_cleanup_task(self):
        """Background task for cache cleanup"""
        while True:
            try:
                await asyncio.sleep(900)  # Run every 15 minutes
                # Cleanup access control cache
                await access_control_manager._clean_access_cache()
                self.log_info("Cache cleanup completed")
            except Exception as e:
                self.log_error(f"Cache cleanup failed: {e}")
    
    # Health check helper methods
    async def _check_langgraph_health(self) -> Dict[str, Any]:
        """Check browser orchestrator health"""
        try:
            # Browser orchestrator is created on-demand, so it's always ready
            return {
                "healthy": True,
                "status": "operational",
                "workflows_available": 4  # Sequential, Supervisor, Hierarchical, Parallel
            }
        except Exception as e:
            return {"healthy": False, "status": "error", "error": str(e)}
    
    async def _check_access_control_health(self) -> Dict[str, Any]:
        """Check access control manager health"""
        try:
            return {
                "healthy": True,
                "status": "operational",
                "active_users": len(access_control_manager.user_quotas),
                "tier_count": len(access_control_manager.tier_limits)
            }
        except Exception as e:
            return {"healthy": False, "status": "error", "error": str(e)}
    
    async def _check_error_handler_health(self) -> Dict[str, Any]:
        """Check error handler health"""
        try:
            return {
                "healthy": True,
                "status": "operational",
                "total_errors": error_handler.error_stats["total_errors"],
                "recovery_rate": error_handler.error_stats.get("recovery_rate", 0)
            }
        except Exception as e:
            return {"healthy": False, "status": "error", "error": str(e)}
    
    async def _check_fallback_manager_health(self) -> Dict[str, Any]:
        """Check fallback manager health"""
        try:
            return {
                "healthy": True,
                "status": "operational",
                "total_fallbacks": fallback_manager.fallback_metrics["total_fallbacks"],
                "success_rate": fallback_manager.fallback_metrics["fallback_success_rate"]
            }
        except Exception as e:
            return {"healthy": False, "status": "error", "error": str(e)}


# Create service instance
service = BrowserAutomationService()
app = service.create_app()


# Development server
if __name__ == "__main__":
    import os
    from dotenv import load_dotenv

    # Load environment variables
    load_dotenv()

    # Get configuration from environment
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    port = int(os.getenv("SERVICE_PORT", "8001"))
    env = os.getenv("SERVICE_ENV", "development")
    reload = env == "development"

    print(f"🚀 Starting RouKey Browser Automation Service")
    print(f"📍 Server: http://{host}:{port}")
    print(f"📚 Docs: http://{host}:{port}/docs")
    print(f"🔄 Reload: {reload}")
    print(f"🌍 Environment: {env}")

    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )
