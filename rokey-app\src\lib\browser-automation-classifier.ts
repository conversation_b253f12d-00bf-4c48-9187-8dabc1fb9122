/**
 * Browser Automation Classifier
 * Detects when user requests require browser automation and classifies task types
 */

export interface BrowserAutomationClassification {
  requiresBrowserAutomation: boolean;
  taskType: 'navigation' | 'data_extraction' | 'form_filling' | 'verification' | 'monitoring' | 'comparison' | 'automation';
  confidence: number;
  extractedParameters: {
    url?: string;
    website?: string;
    searchQuery?: string;
    dataToExtract?: string;
    formData?: Record<string, any>;
    verificationCriteria?: string;
  };
  reasoning: string;
}

export class BrowserAutomationClassifier {
  private static readonly BROWSER_KEYWORDS = [
    // Navigation keywords
    'browse', 'navigate', 'visit', 'go to', 'open website', 'check website',
    'website', 'webpage', 'url', 'link', 'site',
    
    // Data extraction keywords
    'extract', 'scrape', 'get data', 'find information', 'collect data',
    'download', 'grab', 'fetch', 'retrieve', 'pull data',
    
    // Search and research keywords
    'search for', 'look up', 'research', 'find on', 'google', 'bing',
    'search engine', 'web search', 'online search',
    
    // Shopping and comparison keywords
    'price', 'buy', 'purchase', 'shop', 'compare prices', 'cheapest',
    'most expensive', 'best deal', 'discount', 'sale',
    'amazon', 'ebay', 'shopping', 'store', 'marketplace',
    
    // Form filling keywords
    'fill form', 'submit form', 'sign up', 'register', 'login',
    'enter data', 'input', 'form submission',
    
    // Verification keywords
    'verify', 'check if', 'confirm', 'validate', 'cross-reference',
    'fact check', 'double check', 'ensure',
    
    // Monitoring keywords
    'monitor', 'track', 'watch', 'observe', 'check regularly',
    'alert when', 'notify if', 'keep an eye on'
  ];

  private static readonly URL_PATTERNS = [
    /https?:\/\/[^\s]+/gi,
    /www\.[^\s]+/gi,
    /[a-zA-Z0-9-]+\.(com|org|net|edu|gov|io|co|uk|de|fr|jp|cn|au|ca)[^\s]*/gi
  ];

  private static readonly WEBSITE_NAMES = [
    'google', 'amazon', 'ebay', 'facebook', 'twitter', 'linkedin',
    'youtube', 'instagram', 'reddit', 'wikipedia', 'github',
    'stackoverflow', 'medium', 'netflix', 'spotify', 'airbnb',
    'uber', 'lyft', 'booking', 'expedia', 'tripadvisor'
  ];

  public static async classifyMessage(
    message: string,
    conversationContext: string[] = [],
    classificationApiKey?: string
  ): Promise<BrowserAutomationClassification> {
    const messageText = message.toLowerCase();
    
    // Quick keyword-based detection
    const keywordScore = this.calculateKeywordScore(messageText);
    
    // URL detection
    const urlMatches = this.extractUrls(message);
    const hasUrls = urlMatches.length > 0;
    
    // Website name detection
    const websiteMatches = this.detectWebsiteNames(messageText);
    const hasWebsiteNames = websiteMatches.length > 0;
    
    // Calculate base confidence
    let confidence = keywordScore;
    if (hasUrls) confidence += 0.3;
    if (hasWebsiteNames) confidence += 0.2;
    
    // If confidence is low, try LLM classification
    if (confidence < 0.6 && classificationApiKey) {
      const llmResult = await this.llmClassification(message, conversationContext, classificationApiKey);
      if (llmResult.requiresBrowserAutomation) {
        confidence = Math.max(confidence, llmResult.confidence);
      }
    }
    
    const requiresBrowserAutomation = confidence >= 0.5;
    
    if (!requiresBrowserAutomation) {
      return {
        requiresBrowserAutomation: false,
        taskType: 'navigation',
        confidence: confidence,
        extractedParameters: {},
        reasoning: 'No clear browser automation indicators detected'
      };
    }
    
    // Classify task type
    const taskType = this.classifyTaskType(messageText, urlMatches, websiteMatches);
    
    // Extract parameters
    const extractedParameters = this.extractParameters(message, messageText, urlMatches, websiteMatches);
    
    return {
      requiresBrowserAutomation: true,
      taskType,
      confidence: Math.min(confidence, 0.95),
      extractedParameters,
      reasoning: this.generateReasoning(taskType, keywordScore, hasUrls, hasWebsiteNames)
    };
  }

  private static calculateKeywordScore(messageText: string): number {
    const words = messageText.split(/\s+/);
    let matches = 0;
    
    for (const keyword of this.BROWSER_KEYWORDS) {
      if (messageText.includes(keyword)) {
        matches++;
      }
    }
    
    // Normalize score
    return Math.min(matches / 3, 0.8);
  }

  private static extractUrls(message: string): string[] {
    const urls: string[] = [];
    
    for (const pattern of this.URL_PATTERNS) {
      const matches = message.match(pattern);
      if (matches) {
        urls.push(...matches);
      }
    }
    
    return [...new Set(urls)]; // Remove duplicates
  }

  private static detectWebsiteNames(messageText: string): string[] {
    const detected: string[] = [];
    
    for (const website of this.WEBSITE_NAMES) {
      if (messageText.includes(website)) {
        detected.push(website);
      }
    }
    
    return detected;
  }

  private static classifyTaskType(
    messageText: string,
    urlMatches: string[],
    websiteMatches: string[]
  ): BrowserAutomationClassification['taskType'] {
    // Form filling indicators
    if (messageText.includes('fill') || messageText.includes('submit') || 
        messageText.includes('sign up') || messageText.includes('register')) {
      return 'form_filling';
    }
    
    // Data extraction indicators
    if (messageText.includes('extract') || messageText.includes('scrape') || 
        messageText.includes('get data') || messageText.includes('download')) {
      return 'data_extraction';
    }
    
    // Verification indicators
    if (messageText.includes('verify') || messageText.includes('check if') || 
        messageText.includes('confirm') || messageText.includes('validate')) {
      return 'verification';
    }
    
    // Comparison indicators
    if (messageText.includes('compare') || messageText.includes('cheapest') || 
        messageText.includes('best') || messageText.includes('vs')) {
      return 'comparison';
    }
    
    // Monitoring indicators
    if (messageText.includes('monitor') || messageText.includes('track') || 
        messageText.includes('watch') || messageText.includes('alert')) {
      return 'monitoring';
    }
    
    // Shopping indicators
    if (messageText.includes('price') || messageText.includes('buy') || 
        messageText.includes('shop') || websiteMatches.some(w => ['amazon', 'ebay'].includes(w))) {
      return 'data_extraction'; // Shopping is typically data extraction
    }
    
    // Default to navigation
    return 'navigation';
  }

  private static extractParameters(
    originalMessage: string,
    messageText: string,
    urlMatches: string[],
    websiteMatches: string[]
  ): BrowserAutomationClassification['extractedParameters'] {
    const parameters: BrowserAutomationClassification['extractedParameters'] = {};
    
    // Extract URLs
    if (urlMatches.length > 0) {
      parameters.url = urlMatches[0];
    }
    
    // Extract website names
    if (websiteMatches.length > 0) {
      parameters.website = websiteMatches[0];
    }
    
    // Extract search queries (simple heuristic)
    const searchPatterns = [
      /search for "([^"]+)"/i,
      /search for ([^.!?]+)/i,
      /find "([^"]+)"/i,
      /look up "([^"]+)"/i,
      /google "([^"]+)"/i
    ];
    
    for (const pattern of searchPatterns) {
      const match = originalMessage.match(pattern);
      if (match) {
        parameters.searchQuery = match[1].trim();
        break;
      }
    }
    
    // Extract data extraction targets
    const extractPatterns = [
      /extract "([^"]+)"/i,
      /get the ([^.!?]+)/i,
      /find the ([^.!?]+)/i,
      /scrape ([^.!?]+)/i
    ];
    
    for (const pattern of extractPatterns) {
      const match = originalMessage.match(pattern);
      if (match) {
        parameters.dataToExtract = match[1].trim();
        break;
      }
    }
    
    return parameters;
  }

  private static generateReasoning(
    taskType: string,
    keywordScore: number,
    hasUrls: boolean,
    hasWebsiteNames: boolean
  ): string {
    const reasons: string[] = [];
    
    if (keywordScore > 0.3) {
      reasons.push(`Browser automation keywords detected (score: ${keywordScore.toFixed(2)})`);
    }
    
    if (hasUrls) {
      reasons.push('Direct URLs found in message');
    }
    
    if (hasWebsiteNames) {
      reasons.push('Website names mentioned');
    }
    
    reasons.push(`Task classified as: ${taskType}`);
    
    return reasons.join('; ');
  }

  private static async llmClassification(
    message: string,
    conversationContext: string[],
    apiKey: string
  ): Promise<{ requiresBrowserAutomation: boolean; confidence: number }> {
    try {
      const systemPrompt = `You are a browser automation classifier. Determine if the user's request requires browser automation (web browsing, data extraction, form filling, etc.). 
      
Respond with JSON: {"requiresBrowserAutomation": boolean, "confidence": number}

Examples that require browser automation:
- "Check the price of iPhone on Amazon"
- "Fill out this form on the website"
- "Search Google for recent news about AI"
- "Extract data from this webpage"
- "Compare prices between different sites"

Examples that don't require browser automation:
- "Write a story about cats"
- "Solve this math problem"
- "Explain quantum physics"
- "Generate code for a function"`;

      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: 'gemini-1.5-flash',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `Context: ${conversationContext.join(' ')}\n\nCurrent request: ${message}` }
          ],
          temperature: 0.1,
          max_tokens: 100
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();
      const content = result.choices?.[0]?.message?.content;
      
      if (content) {
        const parsed = JSON.parse(content);
        return {
          requiresBrowserAutomation: parsed.requiresBrowserAutomation || false,
          confidence: parsed.confidence || 0.5
        };
      }
    } catch (error) {
      console.warn('LLM classification failed:', error);
    }
    
    return { requiresBrowserAutomation: false, confidence: 0.3 };
  }
}
