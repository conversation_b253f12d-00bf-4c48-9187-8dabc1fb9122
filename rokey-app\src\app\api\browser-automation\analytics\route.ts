import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const timeframe = url.searchParams.get('timeframe') || '30d'; // 7d, 30d, 90d
    const limit = parseInt(url.searchParams.get('limit') || '50');

    // Calculate date range
    const now = new Date();
    const daysBack = timeframe === '7d' ? 7 : timeframe === '90d' ? 90 : 30;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    // Get browser automation tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('browser_automation_tasks')
      .select(`
        id,
        task_description,
        task_type,
        status,
        workflow_type,
        assigned_roles,
        target_url,
        execution_time_seconds,
        steps_completed,
        success,
        created_at,
        completed_at,
        error_details
      `)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })
      .limit(limit);

    if (tasksError) {
      console.error('Error fetching browser automation tasks:', tasksError);
      return NextResponse.json({ error: 'Failed to fetch analytics data' }, { status: 500 });
    }

    // Get analytics events
    const { data: analytics, error: analyticsError } = await supabase
      .from('browser_automation_analytics')
      .select('*')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (analyticsError) {
      console.error('Error fetching analytics events:', analyticsError);
    }

    // Calculate statistics
    const totalTasks = tasks?.length || 0;
    const successfulTasks = tasks?.filter(t => t.success === true).length || 0;
    const failedTasks = tasks?.filter(t => t.success === false).length || 0;
    const pendingTasks = tasks?.filter(t => t.status === 'pending' || t.status === 'running').length || 0;
    
    const successRate = totalTasks > 0 ? (successfulTasks / totalTasks) * 100 : 0;
    
    const avgExecutionTime = tasks?.filter(t => t.execution_time_seconds)
      .reduce((sum, t) => sum + (t.execution_time_seconds || 0), 0) / 
      (tasks?.filter(t => t.execution_time_seconds).length || 1);

    const totalSteps = tasks?.reduce((sum, t) => sum + (t.steps_completed || 0), 0) || 0;
    const avgStepsPerTask = totalTasks > 0 ? totalSteps / totalTasks : 0;

    // Task type distribution
    const taskTypeDistribution = tasks?.reduce((acc: Record<string, number>, task) => {
      acc[task.task_type] = (acc[task.task_type] || 0) + 1;
      return acc;
    }, {}) || {};

    // Workflow type distribution
    const workflowTypeDistribution = tasks?.reduce((acc: Record<string, number>, task) => {
      acc[task.workflow_type] = (acc[task.workflow_type] || 0) + 1;
      return acc;
    }, {}) || {};

    // Daily usage over time
    const dailyUsage = tasks?.reduce((acc: Record<string, number>, task) => {
      const date = task.created_at.split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {}) || {};

    // Most common error types
    const errorTypes = tasks?.filter(t => t.error_details && !t.success)
      .reduce((acc: Record<string, number>, task) => {
        const errorType = task.error_details?.type || 'unknown_error';
        acc[errorType] = (acc[errorType] || 0) + 1;
        return acc;
      }, {}) || {};

    // Performance trends
    const performanceData = tasks?.filter(t => t.execution_time_seconds && t.created_at)
      .map(t => ({
        date: t.created_at.split('T')[0],
        execution_time: t.execution_time_seconds,
        steps: t.steps_completed || 0,
        success: t.success
      })) || [];

    return NextResponse.json({
      timeframe,
      date_range: {
        start: startDate.toISOString(),
        end: now.toISOString()
      },
      summary: {
        total_tasks: totalTasks,
        successful_tasks: successfulTasks,
        failed_tasks: failedTasks,
        pending_tasks: pendingTasks,
        success_rate: Math.round(successRate * 100) / 100,
        avg_execution_time_seconds: Math.round(avgExecutionTime * 100) / 100,
        avg_steps_per_task: Math.round(avgStepsPerTask * 100) / 100,
        total_steps: totalSteps
      },
      distributions: {
        task_types: taskTypeDistribution,
        workflow_types: workflowTypeDistribution,
        error_types: errorTypes
      },
      trends: {
        daily_usage: dailyUsage,
        performance_data: performanceData
      },
      recent_tasks: tasks?.slice(0, 10).map(task => ({
        id: task.id,
        task_description: task.task_description.substring(0, 100) + (task.task_description.length > 100 ? '...' : ''),
        task_type: task.task_type,
        status: task.status,
        success: task.success,
        execution_time_seconds: task.execution_time_seconds,
        steps_completed: task.steps_completed,
        created_at: task.created_at,
        completed_at: task.completed_at
      })) || [],
      insights: [
        ...(successRate < 70 ? ['Consider reviewing failed tasks to improve success rate'] : []),
        ...(avgExecutionTime > 120 ? ['Tasks are taking longer than average - consider optimization'] : []),
        ...(totalTasks === 0 ? ['No browser automation tasks found for this period'] : []),
        ...(pendingTasks > 5 ? ['You have several pending tasks - they may need attention'] : [])
      ]
    });

  } catch (error) {
    console.error('Error fetching browser automation analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
